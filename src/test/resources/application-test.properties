# Test application properties
spring.application.name=user-auth-service-test
server.port=0

# Test Database configuration - Using H2 in-memory database for tests
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=PostgreSQL
spring.datasource.username=sa
spring.datasource.password=
spring.datasource.driver-class-name=org.h2.Driver

# JPA/Hibernate configuration
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect

# Liquibase configuration - Disabled for tests
spring.liquibase.enabled=false

# JWT configuration
jwt.secret=testSecretKey123456789012345678901234567890
jwt.expiration=86400000

# Mail configuration (mocked in tests)
spring.mail.host=localhost
spring.mail.port=25
spring.mail.username=<EMAIL>
spring.mail.password=testpassword
spring.mail.properties.mail.smtp.auth=false
spring.mail.properties.mail.smtp.starttls.enable=false

# Logging
logging.level.com.example.userauth=DEBUG
logging.level.org.jooq=DEBUG
logging.level.org.springframework.web=DEBUG
logging.level.org.testcontainers=INFO

# Test specific properties
spring.test.database.replace=none
