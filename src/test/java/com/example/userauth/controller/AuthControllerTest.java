package com.example.userauth.controller;

import com.example.userauth.dto.request.ForgotPasswordRequest;
import com.example.userauth.dto.request.UserLoginRequest;
import com.example.userauth.dto.request.UserRegistrationRequest;
import com.example.userauth.dto.response.ForgotPasswordResponse;
import com.example.userauth.dto.response.UserLoginResponse;
import com.example.userauth.dto.response.UserRegistrationResponse;
import com.example.userauth.config.TestConfig;
import com.example.userauth.service.UserService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Unit tests for AuthController
 */
@WebMvcTest(controllers = AuthController.class)
@Import(TestConfig.class)
@DisplayName("AuthController Tests")
class AuthControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private UserService userService;

    @Autowired
    private ObjectMapper objectMapper;

    private UserRegistrationRequest validRegistrationRequest;
    private UserLoginRequest validLoginRequest;
    private ForgotPasswordRequest validForgotPasswordRequest;

    @BeforeEach
    void setUp() {
        validRegistrationRequest = UserRegistrationRequest.builder()
                .username("testuser")
                .email("<EMAIL>")
                .password("password123")
                .firstName("Test")
                .lastName("User")
                .build();

        validLoginRequest = UserLoginRequest.builder()
                .usernameOrEmail("testuser")
                .password("password123")
                .build();

        validForgotPasswordRequest = ForgotPasswordRequest.builder()
                .email("<EMAIL>")
                .build();
    }

    @Nested
    @DisplayName("User Registration Tests")
    class UserRegistrationTests {

        @Test
        @DisplayName("Should register user successfully")
        void shouldRegisterUserSuccessfully() throws Exception {
            // Given
            UserRegistrationResponse expectedResponse = UserRegistrationResponse.builder()
                    .id(UUID.randomUUID())
                    .username("testuser")
                    .email("<EMAIL>")
                    .firstName("Test")
                    .lastName("User")
                    .isActive(true)
                    .createdAt(LocalDateTime.now())
                    .message("User registered successfully")
                    .build();

            when(userService.registerUser(any(UserRegistrationRequest.class)))
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(post("/api/auth/register")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(validRegistrationRequest)))
                    .andExpect(status().isCreated())
                    .andExpect(jsonPath("$.username").value("testuser"))
                    .andExpect(jsonPath("$.email").value("<EMAIL>"))
                    .andExpect(jsonPath("$.message").value("User registered successfully"));

            verify(userService, times(1)).registerUser(any(UserRegistrationRequest.class));
        }

        @Test
        @DisplayName("Should return bad request when username already exists")
        void shouldReturnBadRequestWhenUsernameExists() throws Exception {
            // Given
            when(userService.registerUser(any(UserRegistrationRequest.class)))
                    .thenThrow(new RuntimeException("Username already exists"));

            // When & Then
            mockMvc.perform(post("/api/auth/register")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(validRegistrationRequest)))
                    .andExpect(status().isBadRequest())
                    .andExpect(jsonPath("$.message").value("Username already exists"))
                    .andExpect(jsonPath("$.timestamp").exists());

            verify(userService, times(1)).registerUser(any(UserRegistrationRequest.class));
        }

        @Test
        @DisplayName("Should return bad request when email already exists")
        void shouldReturnBadRequestWhenEmailExists() throws Exception {
            // Given
            when(userService.registerUser(any(UserRegistrationRequest.class)))
                    .thenThrow(new RuntimeException("Email already exists"));

            // When & Then
            mockMvc.perform(post("/api/auth/register")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(validRegistrationRequest)))
                    .andExpect(status().isBadRequest())
                    .andExpect(jsonPath("$.message").value("Email already exists"));

            verify(userService, times(1)).registerUser(any(UserRegistrationRequest.class));
        }

        @Test
        @DisplayName("Should return bad request for invalid username")
        void shouldReturnBadRequestForInvalidUsername() throws Exception {
            // Given
            UserRegistrationRequest invalidRequest = UserRegistrationRequest.builder()
                    .username("ab") // Too short
                    .email("<EMAIL>")
                    .password("password123")
                    .firstName("Test")
                    .lastName("User")
                    .build();

            // When & Then
            mockMvc.perform(post("/api/auth/register")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(invalidRequest)))
                    .andExpect(status().isBadRequest());

            verify(userService, never()).registerUser(any(UserRegistrationRequest.class));
        }

        @Test
        @DisplayName("Should return bad request for invalid email")
        void shouldReturnBadRequestForInvalidEmail() throws Exception {
            // Given
            UserRegistrationRequest invalidRequest = UserRegistrationRequest.builder()
                    .username("testuser")
                    .email("invalid-email") // Invalid email format
                    .password("password123")
                    .firstName("Test")
                    .lastName("User")
                    .build();

            // When & Then
            mockMvc.perform(post("/api/auth/register")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(invalidRequest)))
                    .andExpect(status().isBadRequest());

            verify(userService, never()).registerUser(any(UserRegistrationRequest.class));
        }

        @Test
        @DisplayName("Should return bad request for short password")
        void shouldReturnBadRequestForShortPassword() throws Exception {
            // Given
            UserRegistrationRequest invalidRequest = UserRegistrationRequest.builder()
                    .username("testuser")
                    .email("<EMAIL>")
                    .password("12345") // Too short (less than 6 characters)
                    .firstName("Test")
                    .lastName("User")
                    .build();

            // When & Then
            mockMvc.perform(post("/api/auth/register")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(invalidRequest)))
                    .andExpect(status().isBadRequest());

            verify(userService, never()).registerUser(any(UserRegistrationRequest.class));
        }

        @Test
        @DisplayName("Should return bad request for missing required fields")
        void shouldReturnBadRequestForMissingRequiredFields() throws Exception {
            // Given
            UserRegistrationRequest invalidRequest = UserRegistrationRequest.builder()
                    .username("testuser")
                    .email("<EMAIL>")
                    .password("password123")
                    // Missing firstName and lastName
                    .build();

            // When & Then
            mockMvc.perform(post("/api/auth/register")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(invalidRequest)))
                    .andExpect(status().isBadRequest());

            verify(userService, never()).registerUser(any(UserRegistrationRequest.class));
        }

        @Test
        @DisplayName("Should return internal server error for unexpected exception")
        void shouldReturnInternalServerErrorForUnexpectedException() throws Exception {
            // Given
            when(userService.registerUser(any(UserRegistrationRequest.class)))
                    .thenThrow(new IllegalStateException("Database connection failed"));

            // When & Then
            mockMvc.perform(post("/api/auth/register")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(validRegistrationRequest)))
                    .andExpect(status().isInternalServerError())
                    .andExpect(jsonPath("$.message").value("Registration failed due to internal error"));

            verify(userService, times(1)).registerUser(any(UserRegistrationRequest.class));
        }
    }

    @Nested
    @DisplayName("User Login Tests")
    class UserLoginTests {

        @Test
        @DisplayName("Should login user successfully")
        void shouldLoginUserSuccessfully() throws Exception {
            // Given
            UserLoginResponse expectedResponse = UserLoginResponse.builder()
                    .userId(UUID.randomUUID())
                    .username("testuser")
                    .email("<EMAIL>")
                    .firstName("Test")
                    .lastName("User")
                    .accessToken("jwt-token-here")
                    .tokenType("Bearer")
                    .expiresIn(3600L)
                    .message("Login successful")
                    .build();

            when(userService.loginUser(any(UserLoginRequest.class)))
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(post("/api/auth/login")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(validLoginRequest)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.username").value("testuser"))
                    .andExpect(jsonPath("$.accessToken").value("jwt-token-here"))
                    .andExpect(jsonPath("$.tokenType").value("Bearer"))
                    .andExpect(jsonPath("$.message").value("Login successful"));

            verify(userService, times(1)).loginUser(any(UserLoginRequest.class));
        }

        @Test
        @DisplayName("Should return unauthorized for invalid credentials")
        void shouldReturnUnauthorizedForInvalidCredentials() throws Exception {
            // Given
            when(userService.loginUser(any(UserLoginRequest.class)))
                    .thenThrow(new RuntimeException("Invalid credentials"));

            // When & Then
            mockMvc.perform(post("/api/auth/login")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(validLoginRequest)))
                    .andExpect(status().isUnauthorized())
                    .andExpect(jsonPath("$.message").value("Invalid credentials"));

            verify(userService, times(1)).loginUser(any(UserLoginRequest.class));
        }

        @Test
        @DisplayName("Should return bad request for missing username")
        void shouldReturnBadRequestForMissingUsername() throws Exception {
            // Given
            UserLoginRequest invalidRequest = UserLoginRequest.builder()
                    .usernameOrEmail("") // Empty username
                    .password("password123")
                    .build();

            // When & Then
            mockMvc.perform(post("/api/auth/login")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(invalidRequest)))
                    .andExpect(status().isBadRequest());

            verify(userService, never()).loginUser(any(UserLoginRequest.class));
        }

        @Test
        @DisplayName("Should return bad request for missing password")
        void shouldReturnBadRequestForMissingPassword() throws Exception {
            // Given
            UserLoginRequest invalidRequest = UserLoginRequest.builder()
                    .usernameOrEmail("testuser")
                    .password("") // Empty password
                    .build();

            // When & Then
            mockMvc.perform(post("/api/auth/login")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(invalidRequest)))
                    .andExpect(status().isBadRequest());

            verify(userService, never()).loginUser(any(UserLoginRequest.class));
        }

        @Test
        @DisplayName("Should login successfully with email")
        void shouldLoginSuccessfullyWithEmail() throws Exception {
            // Given
            UserLoginRequest emailLoginRequest = UserLoginRequest.builder()
                    .usernameOrEmail("<EMAIL>") // Using email instead of username
                    .password("password123")
                    .build();

            UserLoginResponse expectedResponse = UserLoginResponse.builder()
                    .userId(UUID.randomUUID())
                    .username("testuser")
                    .email("<EMAIL>")
                    .firstName("Test")
                    .lastName("User")
                    .accessToken("jwt-token-here")
                    .tokenType("Bearer")
                    .expiresIn(3600L)
                    .message("Login successful")
                    .build();

            when(userService.loginUser(any(UserLoginRequest.class)))
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(post("/api/auth/login")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(emailLoginRequest)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.username").value("testuser"))
                    .andExpect(jsonPath("$.email").value("<EMAIL>"))
                    .andExpect(jsonPath("$.accessToken").value("jwt-token-here"));

            verify(userService, times(1)).loginUser(any(UserLoginRequest.class));
        }

        @Test
        @DisplayName("Should return internal server error for unexpected exception")
        void shouldReturnInternalServerErrorForUnexpectedException() throws Exception {
            // Given
            when(userService.loginUser(any(UserLoginRequest.class)))
                    .thenThrow(new IllegalStateException("Database connection failed"));

            // When & Then
            mockMvc.perform(post("/api/auth/login")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(validLoginRequest)))
                    .andExpect(status().isInternalServerError())
                    .andExpect(jsonPath("$.message").value("Login failed due to internal error"));

            verify(userService, times(1)).loginUser(any(UserLoginRequest.class));
        }
    }

    @Nested
    @DisplayName("Forgot Password Tests")
    class ForgotPasswordTests {

        @Test
        @DisplayName("Should process forgot password request successfully")
        void shouldProcessForgotPasswordRequestSuccessfully() throws Exception {
            // Given
            ForgotPasswordResponse expectedResponse = ForgotPasswordResponse.builder()
                    .success(true)
                    .message("Password reset link has been sent to your email")
                    .build();

            when(userService.forgotPassword(any(ForgotPasswordRequest.class)))
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(post("/api/auth/forgot-password")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(validForgotPasswordRequest)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.success").value(true))
                    .andExpect(jsonPath("$.message").value("Password reset link has been sent to your email"));

            verify(userService, times(1)).forgotPassword(any(ForgotPasswordRequest.class));
        }

        @Test
        @DisplayName("Should process forgot password request for non-existent email")
        void shouldProcessForgotPasswordRequestForNonExistentEmail() throws Exception {
            // Given
            ForgotPasswordResponse expectedResponse = ForgotPasswordResponse.builder()
                    .success(true)
                    .message("If the email exists, a password reset link has been sent")
                    .build();

            when(userService.forgotPassword(any(ForgotPasswordRequest.class)))
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(post("/api/auth/forgot-password")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(validForgotPasswordRequest)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.success").value(true))
                    .andExpect(jsonPath("$.message").value("If the email exists, a password reset link has been sent"));

            verify(userService, times(1)).forgotPassword(any(ForgotPasswordRequest.class));
        }

        @Test
        @DisplayName("Should return bad request for invalid email format")
        void shouldReturnBadRequestForInvalidEmailFormat() throws Exception {
            // Given
            ForgotPasswordRequest invalidRequest = ForgotPasswordRequest.builder()
                    .email("invalid-email-format") // Invalid email format
                    .build();

            // When & Then
            mockMvc.perform(post("/api/auth/forgot-password")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(invalidRequest)))
                    .andExpect(status().isBadRequest());

            verify(userService, never()).forgotPassword(any(ForgotPasswordRequest.class));
        }

        @Test
        @DisplayName("Should return bad request for empty email")
        void shouldReturnBadRequestForEmptyEmail() throws Exception {
            // Given
            ForgotPasswordRequest invalidRequest = ForgotPasswordRequest.builder()
                    .email("") // Empty email
                    .build();

            // When & Then
            mockMvc.perform(post("/api/auth/forgot-password")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(invalidRequest)))
                    .andExpect(status().isBadRequest());

            verify(userService, never()).forgotPassword(any(ForgotPasswordRequest.class));
        }

        @Test
        @DisplayName("Should return bad request when service throws RuntimeException")
        void shouldReturnBadRequestWhenServiceThrowsRuntimeException() throws Exception {
            // Given
            when(userService.forgotPassword(any(ForgotPasswordRequest.class)))
                    .thenThrow(new RuntimeException("Failed to send password reset email"));

            // When & Then
            mockMvc.perform(post("/api/auth/forgot-password")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(validForgotPasswordRequest)))
                    .andExpect(status().isBadRequest())
                    .andExpect(jsonPath("$.message").value("Failed to send password reset email"));

            verify(userService, times(1)).forgotPassword(any(ForgotPasswordRequest.class));
        }

        @Test
        @DisplayName("Should return internal server error for unexpected exception")
        void shouldReturnInternalServerErrorForUnexpectedException() throws Exception {
            // Given
            when(userService.forgotPassword(any(ForgotPasswordRequest.class)))
                    .thenThrow(new IllegalStateException("Email service unavailable"));

            // When & Then
            mockMvc.perform(post("/api/auth/forgot-password")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(validForgotPasswordRequest)))
                    .andExpect(status().isInternalServerError())
                    .andExpect(jsonPath("$.message").value("Forgot password failed due to internal error"));

            verify(userService, times(1)).forgotPassword(any(ForgotPasswordRequest.class));
        }
    }

    @Nested
    @DisplayName("Health Check Tests")
    class HealthCheckTests {

        @Test
        @DisplayName("Should return health status successfully")
        void shouldReturnHealthStatusSuccessfully() throws Exception {
            // When & Then
            mockMvc.perform(get("/api/auth/health"))
                    .andExpect(status().isOk())
                    .andExpect(content().string("Auth service is running"));

            // No service interaction needed for health check
            verifyNoInteractions(userService);
        }
    }
}
