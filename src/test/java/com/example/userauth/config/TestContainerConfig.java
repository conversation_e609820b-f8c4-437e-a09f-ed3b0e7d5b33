package com.example.userauth.config;

import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.web.SecurityFilterChain;

import static org.mockito.Mockito.mock;

/**
 * Test configuration for integration tests
 */
@TestConfiguration
@EnableWebSecurity
public class TestContainerConfig {

    /**
     * Mock JavaMailSender for testing
     */
    @Bean
    @Primary
    public JavaMailSender mockJavaMailSender() {
        return mock(JavaMailSender.class);
    }

    /**
     * Security configuration for integration tests
     */
    @Bean
    public SecurityFilterChain testSecurityFilterChain(HttpSecurity http) throws Exception {
        http.csrf().disable()
                .authorizeRequests()
                .anyRequest().permitAll();
        return http.build();
    }
}
