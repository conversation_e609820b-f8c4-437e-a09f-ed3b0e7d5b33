package com.example.userauth.integration;

import com.example.userauth.config.TestContainerConfig;
import com.example.userauth.dto.request.ForgotPasswordRequest;
import com.example.userauth.dto.request.UserLoginRequest;
import com.example.userauth.dto.request.UserRegistrationRequest;
import com.example.userauth.dto.response.ForgotPasswordResponse;
import com.example.userauth.dto.response.UserLoginResponse;
import com.example.userauth.dto.response.UserRegistrationResponse;
import com.example.userauth.service.UserService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.time.LocalDateTime;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Simple Integration tests for AuthController
 * Tests the HTTP layer with mocked service layer
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Import(TestContainerConfig.class)
@ActiveProfiles("test")
@AutoConfigureWebMvc
@DisplayName("AuthController Simple Integration Tests")
class AuthControllerSimpleIntegrationTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private UserService userService;

    private MockMvc mockMvc;

    private UserRegistrationRequest validRegistrationRequest;
    private UserLoginRequest validLoginRequest;
    private ForgotPasswordRequest validForgotPasswordRequest;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        // Reset mocks
        reset(userService);

        // Setup test data
        validRegistrationRequest = UserRegistrationRequest.builder()
                .username("testuser")
                .email("<EMAIL>")
                .password("password123")
                .firstName("Test")
                .lastName("User")
                .build();

        validLoginRequest = UserLoginRequest.builder()
                .usernameOrEmail("testuser")
                .password("password123")
                .build();

        validForgotPasswordRequest = ForgotPasswordRequest.builder()
                .email("<EMAIL>")
                .build();
    }

    @Nested
    @DisplayName("User Registration Integration Tests")
    class UserRegistrationIntegrationTests {

        @Test
        @DisplayName("Should register user successfully through full HTTP flow")
        void shouldRegisterUserSuccessfullyThroughFullHttpFlow() throws Exception {
            // Given
            UserRegistrationResponse expectedResponse = UserRegistrationResponse.builder()
                    .id(UUID.randomUUID())
                    .username("testuser")
                    .email("<EMAIL>")
                    .firstName("Test")
                    .lastName("User")
                    .isActive(true)
                    .createdAt(LocalDateTime.now())
                    .message("User registered successfully")
                    .build();

            when(userService.registerUser(any(UserRegistrationRequest.class)))
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(post("/api/auth/register")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(validRegistrationRequest)))
                    .andExpect(status().isCreated())
                    .andExpect(jsonPath("$.username").value("testuser"))
                    .andExpect(jsonPath("$.email").value("<EMAIL>"))
                    .andExpect(jsonPath("$.firstName").value("Test"))
                    .andExpect(jsonPath("$.lastName").value("User"))
                    .andExpect(jsonPath("$.isActive").value(true))
                    .andExpect(jsonPath("$.id").exists())
                    .andExpect(jsonPath("$.createdAt").exists())
                    .andExpect(jsonPath("$.message").value("User registered successfully"));

            verify(userService, times(1)).registerUser(any(UserRegistrationRequest.class));
        }

        @Test
        @DisplayName("Should handle duplicate username through full HTTP flow")
        void shouldHandleDuplicateUsernameThroughFullHttpFlow() throws Exception {
            // Given
            when(userService.registerUser(any(UserRegistrationRequest.class)))
                    .thenThrow(new RuntimeException("Username already exists"));

            // When & Then
            mockMvc.perform(post("/api/auth/register")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(validRegistrationRequest)))
                    .andExpect(status().isBadRequest())
                    .andExpect(jsonPath("$.message").value("Username already exists"))
                    .andExpect(jsonPath("$.timestamp").exists());

            verify(userService, times(1)).registerUser(any(UserRegistrationRequest.class));
        }

        @Test
        @DisplayName("Should validate request data through full HTTP flow")
        void shouldValidateRequestDataThroughFullHttpFlow() throws Exception {
            // Given - Invalid request with missing fields
            UserRegistrationRequest invalidRequest = UserRegistrationRequest.builder()
                    .username("ab") // Too short
                    .email("invalid-email") // Invalid email format
                    .password("123") // Too short password
                    .build(); // Missing firstName and lastName

            // When & Then
            mockMvc.perform(post("/api/auth/register")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(invalidRequest)))
                    .andExpect(status().isBadRequest());

            verify(userService, never()).registerUser(any(UserRegistrationRequest.class));
        }
    }

    @Nested
    @DisplayName("User Login Integration Tests")
    class UserLoginIntegrationTests {

        @Test
        @DisplayName("Should login user successfully through full HTTP flow")
        void shouldLoginUserSuccessfullyThroughFullHttpFlow() throws Exception {
            // Given
            UserLoginResponse expectedResponse = UserLoginResponse.builder()
                    .userId(UUID.randomUUID())
                    .username("testuser")
                    .email("<EMAIL>")
                    .firstName("Test")
                    .lastName("User")
                    .accessToken("jwt-token-here")
                    .tokenType("Bearer")
                    .expiresIn(3600L)
                    .message("Login successful")
                    .build();

            when(userService.loginUser(any(UserLoginRequest.class)))
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(post("/api/auth/login")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(validLoginRequest)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.username").value("testuser"))
                    .andExpect(jsonPath("$.email").value("<EMAIL>"))
                    .andExpect(jsonPath("$.accessToken").value("jwt-token-here"))
                    .andExpect(jsonPath("$.tokenType").value("Bearer"))
                    .andExpect(jsonPath("$.expiresIn").value(3600L))
                    .andExpect(jsonPath("$.message").value("Login successful"));

            verify(userService, times(1)).loginUser(any(UserLoginRequest.class));
        }

        @Test
        @DisplayName("Should handle invalid credentials through full HTTP flow")
        void shouldHandleInvalidCredentialsThroughFullHttpFlow() throws Exception {
            // Given
            when(userService.loginUser(any(UserLoginRequest.class)))
                    .thenThrow(new RuntimeException("Invalid credentials"));

            // When & Then
            mockMvc.perform(post("/api/auth/login")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(validLoginRequest)))
                    .andExpect(status().isUnauthorized())
                    .andExpect(jsonPath("$.message").value("Invalid credentials"));

            verify(userService, times(1)).loginUser(any(UserLoginRequest.class));
        }

        @Test
        @DisplayName("Should validate login request through full HTTP flow")
        void shouldValidateLoginRequestThroughFullHttpFlow() throws Exception {
            // Given - Invalid login request
            UserLoginRequest invalidRequest = UserLoginRequest.builder()
                    .usernameOrEmail("") // Empty username
                    .password("") // Empty password
                    .build();

            // When & Then
            mockMvc.perform(post("/api/auth/login")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(invalidRequest)))
                    .andExpect(status().isBadRequest());

            verify(userService, never()).loginUser(any(UserLoginRequest.class));
        }
    }

    @Nested
    @DisplayName("Forgot Password Integration Tests")
    class ForgotPasswordIntegrationTests {

        @Test
        @DisplayName("Should process forgot password request through full HTTP flow")
        void shouldProcessForgotPasswordRequestThroughFullHttpFlow() throws Exception {
            // Given
            ForgotPasswordResponse expectedResponse = ForgotPasswordResponse.builder()
                    .success(true)
                    .message("Password reset link has been sent to your email")
                    .build();

            when(userService.forgotPassword(any(ForgotPasswordRequest.class)))
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(post("/api/auth/forgot-password")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(validForgotPasswordRequest)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.success").value(true))
                    .andExpect(jsonPath("$.message").value("Password reset link has been sent to your email"));

            verify(userService, times(1)).forgotPassword(any(ForgotPasswordRequest.class));
        }

        @Test
        @DisplayName("Should handle email service failure through full HTTP flow")
        void shouldHandleEmailServiceFailureThroughFullHttpFlow() throws Exception {
            // Given
            when(userService.forgotPassword(any(ForgotPasswordRequest.class)))
                    .thenThrow(new RuntimeException("Failed to send password reset email"));

            // When & Then
            mockMvc.perform(post("/api/auth/forgot-password")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(validForgotPasswordRequest)))
                    .andExpect(status().isBadRequest())
                    .andExpect(jsonPath("$.message").value("Failed to send password reset email"));

            verify(userService, times(1)).forgotPassword(any(ForgotPasswordRequest.class));
        }

        @Test
        @DisplayName("Should validate email format through full HTTP flow")
        void shouldValidateEmailFormatThroughFullHttpFlow() throws Exception {
            // Given - Invalid email format
            ForgotPasswordRequest invalidRequest = ForgotPasswordRequest.builder()
                    .email("invalid-email-format")
                    .build();

            // When & Then
            mockMvc.perform(post("/api/auth/forgot-password")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(invalidRequest)))
                    .andExpect(status().isBadRequest());

            verify(userService, never()).forgotPassword(any(ForgotPasswordRequest.class));
        }
    }

    @Nested
    @DisplayName("Health Check Integration Tests")
    class HealthCheckIntegrationTests {

        @Test
        @DisplayName("Should return health status through full HTTP flow")
        void shouldReturnHealthStatusThroughFullHttpFlow() throws Exception {
            // When & Then
            mockMvc.perform(get("/api/auth/health"))
                    .andExpect(status().isOk())
                    .andExpect(content().string("Auth service is running"));

            // No service interaction needed for health check
            verifyNoInteractions(userService);
        }
    }
}
