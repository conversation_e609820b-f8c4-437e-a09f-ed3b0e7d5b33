package com.example.userauth.integration;

import com.example.userauth.config.TestContainerConfig;
import com.example.userauth.dto.request.ForgotPasswordRequest;
import com.example.userauth.dto.request.UserLoginRequest;
import com.example.userauth.dto.request.UserRegistrationRequest;
import com.example.userauth.repository.UserRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jooq.DSLContext;
import org.jooq.Record;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import java.util.Optional;
import java.util.UUID;

import static com.example.userauth.db.jooq.Tables.USERS;
import static org.hamcrest.Matchers.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration tests for AuthController
 * Tests the full flow from HTTP request to database using H2 in-memory database
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Import(TestContainerConfig.class)
@ActiveProfiles("test")
@AutoConfigureWebMvc
@DisplayName("AuthController Integration Tests")
class AuthControllerIntegrationTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private DSLContext dslContext;

    @Autowired
    private JavaMailSender mailSender;

    private MockMvc mockMvc;

    private UserRegistrationRequest validRegistrationRequest;
    private UserLoginRequest validLoginRequest;
    private ForgotPasswordRequest validForgotPasswordRequest;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        // Reset mocks
        reset(mailSender);

        // Clean database before each test
        dslContext.deleteFrom(USERS).execute();

        // Setup test data
        validRegistrationRequest = UserRegistrationRequest.builder()
                .username("testuser")
                .email("<EMAIL>")
                .password("password123")
                .firstName("Test")
                .lastName("User")
                .build();

        validLoginRequest = UserLoginRequest.builder()
                .usernameOrEmail("testuser")
                .password("password123")
                .build();

        validForgotPasswordRequest = ForgotPasswordRequest.builder()
                .email("<EMAIL>")
                .build();
    }

    @Nested
    @DisplayName("User Registration Integration Tests")
    class UserRegistrationIntegrationTests {

        @Test
        @DisplayName("Should register user successfully and persist to database")
        void shouldRegisterUserSuccessfullyAndPersistToDatabase() throws Exception {
            // When & Then
            mockMvc.perform(post("/api/auth/register")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(validRegistrationRequest)))
                    .andExpect(status().isCreated())
                    .andExpect(jsonPath("$.username").value("testuser"))
                    .andExpect(jsonPath("$.email").value("<EMAIL>"))
                    .andExpect(jsonPath("$.firstName").value("Test"))
                    .andExpect(jsonPath("$.lastName").value("User"))
                    .andExpect(jsonPath("$.isActive").value(true))
                    .andExpect(jsonPath("$.id").exists())
                    .andExpect(jsonPath("$.createdAt").exists())
                    .andExpect(jsonPath("$.message").value("User registered successfully"));

            // Verify user exists in database
            Optional<Record> userRecord = userRepository.findByUsername("testuser");
            assertTrue(userRecord.isPresent());
            
            Record user = userRecord.get();
            assertEquals("testuser", user.get(USERS.USERNAME));
            assertEquals("<EMAIL>", user.get(USERS.EMAIL));
            assertEquals("Test", user.get(USERS.FIRST_NAME));
            assertEquals("User", user.get(USERS.LAST_NAME));
            assertTrue(user.get(USERS.IS_ACTIVE));
            assertNotNull(user.get(USERS.PASSWORD_HASH));
            assertNotEquals("password123", user.get(USERS.PASSWORD_HASH)); // Password should be encoded
        }

        @Test
        @DisplayName("Should prevent duplicate username registration")
        void shouldPreventDuplicateUsernameRegistration() throws Exception {
            // Given - Register first user
            userRepository.saveUser("testuser", "<EMAIL>", "hashedPassword", "First", "User");

            // When & Then - Try to register with same username
            mockMvc.perform(post("/api/auth/register")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(validRegistrationRequest)))
                    .andExpect(status().isBadRequest())
                    .andExpect(jsonPath("$.message").value("Username already exists"));

            // Verify only one user exists in database
            long userCount = dslContext.selectCount().from(USERS).fetchOne(0, Long.class);
            assertEquals(1, userCount);
        }

        @Test
        @DisplayName("Should prevent duplicate email registration")
        void shouldPreventDuplicateEmailRegistration() throws Exception {
            // Given - Register first user
            userRepository.saveUser("firstuser", "<EMAIL>", "hashedPassword", "First", "User");

            // When & Then - Try to register with same email
            mockMvc.perform(post("/api/auth/register")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(validRegistrationRequest)))
                    .andExpect(status().isBadRequest())
                    .andExpect(jsonPath("$.message").value("Email already exists"));

            // Verify only one user exists in database
            long userCount = dslContext.selectCount().from(USERS).fetchOne(0, Long.class);
            assertEquals(1, userCount);
        }

        @Test
        @DisplayName("Should validate required fields")
        void shouldValidateRequiredFields() throws Exception {
            // Given - Invalid request with missing fields
            UserRegistrationRequest invalidRequest = UserRegistrationRequest.builder()
                    .username("") // Empty username
                    .email("invalid-email") // Invalid email format
                    .password("123") // Too short password
                    .build(); // Missing firstName and lastName

            // When & Then
            mockMvc.perform(post("/api/auth/register")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(invalidRequest)))
                    .andExpect(status().isBadRequest());

            // Verify no user was created in database
            long userCount = dslContext.selectCount().from(USERS).fetchOne(0, Long.class);
            assertEquals(0, userCount);
        }

        @Test
        @DisplayName("Should handle concurrent registration attempts")
        void shouldHandleConcurrentRegistrationAttempts() throws Exception {
            // This test verifies database constraints work correctly
            // Even if two requests try to register the same username simultaneously

            // When & Then - First registration should succeed
            mockMvc.perform(post("/api/auth/register")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(validRegistrationRequest)))
                    .andExpect(status().isCreated());

            // Second registration with same username should fail
            mockMvc.perform(post("/api/auth/register")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(validRegistrationRequest)))
                    .andExpect(status().isBadRequest())
                    .andExpect(jsonPath("$.message").value("Username already exists"));

            // Verify only one user exists
            long userCount = dslContext.selectCount().from(USERS).fetchOne(0, Long.class);
            assertEquals(1, userCount);
        }
    }

    @Nested
    @DisplayName("User Login Integration Tests")
    class UserLoginIntegrationTests {

        @Test
        @DisplayName("Should login user successfully with username and return JWT token")
        void shouldLoginUserSuccessfullyWithUsernameAndReturnJwtToken() throws Exception {
            // Given - Register a user first
            mockMvc.perform(post("/api/auth/register")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(validRegistrationRequest)))
                    .andExpect(status().isCreated());

            // When & Then - Login with username
            mockMvc.perform(post("/api/auth/login")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(validLoginRequest)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.username").value("testuser"))
                    .andExpect(jsonPath("$.email").value("<EMAIL>"))
                    .andExpect(jsonPath("$.firstName").value("Test"))
                    .andExpect(jsonPath("$.lastName").value("User"))
                    .andExpect(jsonPath("$.accessToken").exists())
                    .andExpect(jsonPath("$.accessToken").isNotEmpty())
                    .andExpect(jsonPath("$.tokenType").value("Bearer"))
                    .andExpect(jsonPath("$.expiresIn").value(86400L))
                    .andExpect(jsonPath("$.userId").exists())
                    .andExpect(jsonPath("$.message").value("Login successful"));
        }

        @Test
        @DisplayName("Should login user successfully with email")
        void shouldLoginUserSuccessfullyWithEmail() throws Exception {
            // Given - Register a user first
            mockMvc.perform(post("/api/auth/register")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(validRegistrationRequest)))
                    .andExpect(status().isCreated());

            // When - Login with email instead of username
            UserLoginRequest emailLoginRequest = UserLoginRequest.builder()
                    .usernameOrEmail("<EMAIL>")
                    .password("password123")
                    .build();

            // Then
            mockMvc.perform(post("/api/auth/login")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(emailLoginRequest)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.username").value("testuser"))
                    .andExpect(jsonPath("$.email").value("<EMAIL>"))
                    .andExpect(jsonPath("$.accessToken").exists())
                    .andExpect(jsonPath("$.tokenType").value("Bearer"));
        }

        @Test
        @DisplayName("Should reject login with invalid username")
        void shouldRejectLoginWithInvalidUsername() throws Exception {
            // Given - Register a user first
            mockMvc.perform(post("/api/auth/register")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(validRegistrationRequest)))
                    .andExpect(status().isCreated());

            // When - Try to login with non-existent username
            UserLoginRequest invalidRequest = UserLoginRequest.builder()
                    .usernameOrEmail("nonexistentuser")
                    .password("password123")
                    .build();

            // Then
            mockMvc.perform(post("/api/auth/login")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(invalidRequest)))
                    .andExpect(status().isUnauthorized())
                    .andExpect(jsonPath("$.message").value("Invalid credentials"));
        }

        @Test
        @DisplayName("Should reject login with invalid password")
        void shouldRejectLoginWithInvalidPassword() throws Exception {
            // Given - Register a user first
            mockMvc.perform(post("/api/auth/register")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(validRegistrationRequest)))
                    .andExpect(status().isCreated());

            // When - Try to login with wrong password
            UserLoginRequest invalidRequest = UserLoginRequest.builder()
                    .usernameOrEmail("testuser")
                    .password("wrongpassword")
                    .build();

            // Then
            mockMvc.perform(post("/api/auth/login")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(invalidRequest)))
                    .andExpect(status().isUnauthorized())
                    .andExpect(jsonPath("$.message").value("Invalid credentials"));
        }

        @Test
        @DisplayName("Should validate login request fields")
        void shouldValidateLoginRequestFields() throws Exception {
            // Given - Invalid login request
            UserLoginRequest invalidRequest = UserLoginRequest.builder()
                    .usernameOrEmail("") // Empty username
                    .password("") // Empty password
                    .build();

            // When & Then
            mockMvc.perform(post("/api/auth/login")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(invalidRequest)))
                    .andExpect(status().isBadRequest());
        }
    }

    @Nested
    @DisplayName("Forgot Password Integration Tests")
    class ForgotPasswordIntegrationTests {

        @Test
        @DisplayName("Should process forgot password request and send email for existing user")
        void shouldProcessForgotPasswordRequestAndSendEmailForExistingUser() throws Exception {
            // Given - Register a user first
            mockMvc.perform(post("/api/auth/register")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(validRegistrationRequest)))
                    .andExpect(status().isCreated());

            // When & Then - Request password reset
            mockMvc.perform(post("/api/auth/forgot-password")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(validForgotPasswordRequest)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.success").value(true))
                    .andExpect(jsonPath("$.message").value("Password reset link has been sent to your email"));

            // Verify email was sent
            verify(mailSender, times(1)).send(any(SimpleMailMessage.class));

            // Verify reset token was saved in database
            Optional<Record> userRecord = userRepository.findByEmail("<EMAIL>");
            assertTrue(userRecord.isPresent());

            Record user = userRecord.get();
            assertNotNull(user.get(USERS.RESET_TOKEN));
            assertNotNull(user.get(USERS.RESET_TOKEN_EXPIRY));
        }

        @Test
        @DisplayName("Should handle forgot password request for non-existent email gracefully")
        void shouldHandleForgotPasswordRequestForNonExistentEmailGracefully() throws Exception {
            // Given - No user registered
            ForgotPasswordRequest nonExistentEmailRequest = ForgotPasswordRequest.builder()
                    .email("<EMAIL>")
                    .build();

            // When & Then - Request password reset for non-existent email
            mockMvc.perform(post("/api/auth/forgot-password")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(nonExistentEmailRequest)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.success").value(true))
                    .andExpect(jsonPath("$.message").value("If the email exists, a password reset link has been sent"));

            // Verify no email was sent
            verify(mailSender, never()).send(any(SimpleMailMessage.class));

            // Verify no reset token was created
            long userCount = dslContext.selectCount().from(USERS).fetchOne(0, Long.class);
            assertEquals(0, userCount);
        }

        @Test
        @DisplayName("Should validate email format in forgot password request")
        void shouldValidateEmailFormatInForgotPasswordRequest() throws Exception {
            // Given - Invalid email format
            ForgotPasswordRequest invalidRequest = ForgotPasswordRequest.builder()
                    .email("invalid-email-format")
                    .build();

            // When & Then
            mockMvc.perform(post("/api/auth/forgot-password")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(invalidRequest)))
                    .andExpect(status().isBadRequest());

            // Verify no email was sent
            verify(mailSender, never()).send(any(SimpleMailMessage.class));
        }

        @Test
        @DisplayName("Should handle email service failure gracefully")
        void shouldHandleEmailServiceFailureGracefully() throws Exception {
            // Given - Register a user first
            mockMvc.perform(post("/api/auth/register")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(validRegistrationRequest)))
                    .andExpect(status().isCreated());

            // Mock email service to throw exception
            doThrow(new RuntimeException("Email service unavailable"))
                    .when(mailSender).send(any(SimpleMailMessage.class));

            // When & Then - Request password reset
            mockMvc.perform(post("/api/auth/forgot-password")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(validForgotPasswordRequest)))
                    .andExpect(status().isBadRequest())
                    .andExpect(jsonPath("$.message").value("Failed to send password reset email"));

            // Verify reset token was still saved (transaction should not rollback)
            Optional<Record> userRecord = userRepository.findByEmail("<EMAIL>");
            assertTrue(userRecord.isPresent());

            Record user = userRecord.get();
            assertNotNull(user.get(USERS.RESET_TOKEN));
            assertNotNull(user.get(USERS.RESET_TOKEN_EXPIRY));
        }
    }

    @Nested
    @DisplayName("Database Transaction Integration Tests")
    class DatabaseTransactionIntegrationTests {

        @Test
        @DisplayName("Should rollback registration transaction on database error")
        @Transactional
        void shouldRollbackRegistrationTransactionOnDatabaseError() throws Exception {
            // This test verifies that if there's a database error during registration,
            // the transaction is properly rolled back and no partial data is saved

            // Given - Create a user with the same username to cause constraint violation
            userRepository.saveUser("testuser", "<EMAIL>", "hashedPassword", "Existing", "User");

            // When & Then - Try to register with duplicate username
            mockMvc.perform(post("/api/auth/register")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(validRegistrationRequest)))
                    .andExpect(status().isBadRequest())
                    .andExpect(jsonPath("$.message").value("Username already exists"));

            // Verify only the original user exists (no partial registration)
            long userCount = dslContext.selectCount().from(USERS).fetchOne(0, Long.class);
            assertEquals(1, userCount);

            Optional<Record> userRecord = userRepository.findByEmail("<EMAIL>");
            assertTrue(userRecord.isPresent());

            Optional<Record> duplicateRecord = userRepository.findByEmail("<EMAIL>");
            assertFalse(duplicateRecord.isPresent());
        }

        @Test
        @DisplayName("Should handle multiple users registration correctly")
        void shouldHandleMultipleUsersRegistrationCorrectly() throws Exception {
            // Given - Multiple user registration requests
            UserRegistrationRequest user1 = UserRegistrationRequest.builder()
                    .username("user1")
                    .email("<EMAIL>")
                    .password("password123")
                    .firstName("User")
                    .lastName("One")
                    .build();

            UserRegistrationRequest user2 = UserRegistrationRequest.builder()
                    .username("user2")
                    .email("<EMAIL>")
                    .password("password123")
                    .firstName("User")
                    .lastName("Two")
                    .build();

            // When - Register multiple users
            mockMvc.perform(post("/api/auth/register")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(user1)))
                    .andExpect(status().isCreated());

            mockMvc.perform(post("/api/auth/register")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(user2)))
                    .andExpect(status().isCreated());

            // Then - Verify both users exist in database
            long userCount = dslContext.selectCount().from(USERS).fetchOne(0, Long.class);
            assertEquals(2, userCount);

            assertTrue(userRepository.findByUsername("user1").isPresent());
            assertTrue(userRepository.findByUsername("user2").isPresent());
            assertTrue(userRepository.findByEmail("<EMAIL>").isPresent());
            assertTrue(userRepository.findByEmail("<EMAIL>").isPresent());
        }
    }

    @Nested
    @DisplayName("Security Integration Tests")
    class SecurityIntegrationTests {

        @Test
        @DisplayName("Should generate valid JWT token on successful login")
        void shouldGenerateValidJwtTokenOnSuccessfulLogin() throws Exception {
            // Given - Register a user first
            mockMvc.perform(post("/api/auth/register")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(validRegistrationRequest)))
                    .andExpect(status().isCreated());

            // When - Login and extract token
            String response = mockMvc.perform(post("/api/auth/login")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(validLoginRequest)))
                    .andExpect(status().isOk())
                    .andReturn()
                    .getResponse()
                    .getContentAsString();

            // Then - Verify token structure and properties
            var loginResponse = objectMapper.readTree(response);
            String accessToken = loginResponse.get("accessToken").asText();
            String tokenType = loginResponse.get("tokenType").asText();
            long expiresIn = loginResponse.get("expiresIn").asLong();

            assertNotNull(accessToken);
            assertFalse(accessToken.isEmpty());
            assertEquals("Bearer", tokenType);
            assertEquals(86400L, expiresIn); // 24 hours in seconds

            // JWT tokens should have 3 parts separated by dots
            String[] tokenParts = accessToken.split("\\.");
            assertEquals(3, tokenParts.length);
        }

        @Test
        @DisplayName("Should properly encode passwords")
        void shouldProperlyEncodePasswords() throws Exception {
            // Given & When - Register a user
            mockMvc.perform(post("/api/auth/register")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(validRegistrationRequest)))
                    .andExpect(status().isCreated());

            // Then - Verify password is encoded in database
            Optional<Record> userRecord = userRepository.findByUsername("testuser");
            assertTrue(userRecord.isPresent());

            Record user = userRecord.get();
            String storedPasswordHash = user.get(USERS.PASSWORD_HASH);

            assertNotNull(storedPasswordHash);
            assertNotEquals("password123", storedPasswordHash); // Should not be plain text
            assertTrue(storedPasswordHash.startsWith("$2a$")); // BCrypt hash format
        }

        @Test
        @DisplayName("Should handle health endpoint without authentication")
        void shouldHandleHealthEndpointWithoutAuthentication() throws Exception {
            // When & Then - Health endpoint should be accessible without authentication
            mockMvc.perform(get("/api/auth/health"))
                    .andExpect(status().isOk())
                    .andExpect(content().string("Auth service is running"));
        }
    }
}
